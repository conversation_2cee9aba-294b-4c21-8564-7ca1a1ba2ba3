# Auto detect text files and perform LF normalization
* text=auto

# Mark generated files so GitHub doesn't show them in diffs
*.lock binary
package-lock.json binary
yarn.lock binary

# Don't diff or merge these files
*.min.js binary
*.min.css binary

# Mark the following as generated files
.next/** linguist-generated=true
out/** linguist-generated=true
build/** linguist-generated=true
dist/** linguist-generated=true
public/sw.js linguist-generated=true
public/workbox-*.js linguist-generated=true
public/worker-*.js linguist-generated=true

# Treat SVGs as binary to avoid line ending issues
*.svg binary
