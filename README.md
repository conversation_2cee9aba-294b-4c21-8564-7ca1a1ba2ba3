# GUS EIP Logger Frontend

A Next.js application with App Router architecture and Tailwind CSS for displaying logging information from integration platforms. Built for deployment on AWS S3 static hosting.

## Features

- Modern UI with Tailwind CSS and Shadcn UI components
- Responsive design for all screen sizes
- Table view of integration logs with filtering
- Status indicators with color-coding
- Optimized for AWS S3 static website hosting
- Client-side rendering for URL parameter support

## Getting Started

First, install the dependencies:

```bash
npm install
# or
yarn install
```

Then, run the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

To view logs, use URL parameters:
[http://localhost:3000/?entityKey=YOUR_ENTITY_ID&entityType=YOUR_ENTITY_TYPE](http://localhost:3000/?entityKey=YOUR_ENTITY_ID&entityType=YOUR_ENTITY_TYPE)

For example:
[http://localhost:3000/?entityKey=b98a4c2e-3a92-4be5-be5a-c1225696ddec&entityType=Application_Form_Id\_\_c](http://localhost:3000/?entityKey=b98a4c2e-3a92-4be5-be5a-c1225696ddec&entityType=Application_Form_Id__c)

## Deploying to AWS S3

### 1. Build the project

```bash
npm run build
# or
yarn build
```

This will create a static export in the `out` directory.

### 2. Configure S3 Bucket for Static Website Hosting

1. Create a new S3 bucket in the AWS Console
2. In the "Properties" tab, enable "Static website hosting"
3. Set "Index document" to `index.html`
4. Set "Error document" to `404.html`

### 3. Set Bucket Permissions

Add a bucket policy to make the content publicly accessible:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::YOUR-BUCKET-NAME/*"
    }
  ]
}
```

### 4. Upload the Content

Upload all files from the `out` directory to your S3 bucket:

```bash
aws s3 sync out/ s3://YOUR-BUCKET-NAME
```

Or use the AWS Console to upload the files manually.

### 5. Access Your Website

Your website will be available at the S3 website endpoint:
`http://YOUR-BUCKET-NAME.s3-website-REGION.amazonaws.com`

## API Integration

In a production environment, update the `LogsTable.tsx` component to fetch data from your actual API endpoint instead of using the mock data. Look for the `useEffect` hook in that file and uncomment the fetch call.

## Learn More

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API
- [Tailwind CSS Documentation](https://tailwindcss.com/docs) - learn about Tailwind CSS
- [Shadcn UI](https://ui.shadcn.com) - the UI components library used in this project
- [AWS S3 Static Website Hosting](https://docs.aws.amazon.com/AmazonS3/latest/userguide/WebsiteHosting.html) - hosting static websites on S3
