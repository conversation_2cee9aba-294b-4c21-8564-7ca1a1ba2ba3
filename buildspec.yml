version: 0.2

env:
  variables:
    S3_BUCKET_DEV: "gus-eip-logger-frontend-dev"
    S3_BUCKET_PROD: "gus-eip-logger-frontend-prod"
    CLOUDFRONT_ID_DEV: "E3FAZR8JJCGR26"
    CLOUDFRONT_ID_PROD: "E825543TU1EF8"

phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      - npm install --legacy-peer-deps

  pre_build:
    commands:
      # Set environment-specific variables based on stage
      - |
        if [ "$stage" = "prod" ]; then
          export S3_BUCKET=$S3_BUCKET_PROD
          export CLOUDFRONT_ID=$CLOUDFRONT_ID_PROD
          echo "Building for production environment"
          npm run build
        else
          export S3_BUCKET=$S3_BUCKET_DEV
          export CLOUDFRONT_ID=$CLOUDFRONT_ID_DEV
          echo "Building for development environment"
          npm run build
        fi

  build:
    commands:
      # Copy the content of build folder into appropriate S3 bucket
      - aws s3 sync out/ s3://${S3_BUCKET}/ --delete
      - aws cloudfront create-invalidation --distribution-id=${CLOUDFRONT_ID} --paths '/*'

  post_build:
    commands:
