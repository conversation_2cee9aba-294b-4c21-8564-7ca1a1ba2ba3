"use client";

import { useEffect, useState, useCallback } from "react";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./ui/table";
import { LogEntry, LogResponse } from "../types/logs";
import { parse, compareDesc } from "date-fns";
import { RefreshCw } from "lucide-react";

// Helper function to determine badge color based on log type
function getStatusBadgeVariant(type: string) {
  type = type.toLowerCase();
  if (type === "info") return "info" as const;
  if (type === "error") return "destructive" as const;
  if (type === "warning") return "warning" as const;
  if (type === "success") return "success" as const;
  return "secondary" as const;
}

function getStatusText(status: string) {
  return status.includes("Successfully") ||
    status.includes("Completed Successfully")
    ? ("success" as const)
    : ("destructive" as const);
}

export default function LogsTable({
  entityKey,
  entityType,
  authToken,
}: {
  entityKey: string;
  entityType: string;
  authToken: string;
}) {
  const [loading, setLoading] = useState(true);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [error, setError] = useState<string | null>(null);

  const fetchLogs = useCallback(async () => {
    try {
      setLoading(true);
      // Use the actual GUS EIP API endpoint
      const response = await fetch(
        `https://dev-api.guseip.io/eip/logs?entityKey=${entityKey}&entityType=${entityType}`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data: LogResponse = await response.json();

      // Filter out logs where any required field is blank
      const filteredLogs = data.data.filter((log) => {
        // Helper function to check if a field is blank or just a dash
        const isFieldBlank = (value: string | undefined) => {
          return !value || value.trim() === "" || value.trim() === "-";
        };

        // Check all fields in the table header in the order they appear
        // If any of these required fields are blank, we'll exclude the log
        const hasTimestamp = !isFieldBlank(log.timestamp);
        const hasType = !isFieldBlank(log.type);
        const hasStatus = !isFieldBlank(log.status);
        const hasSource = !isFieldBlank(log.source);
        const hasDestination = !isFieldBlank(log.destination);
        const hasUsecase = !isFieldBlank(log.usecase);
        const hasMessage = !isFieldBlank(log.message);

        // A log is valid only if ALL required fields have values
        return (
          hasTimestamp &&
          hasType &&
          hasStatus &&
          hasSource &&
          hasDestination &&
          hasUsecase &&
          hasMessage
        );
      });

      // Sort logs by timestamp (newest first)
      const sortedLogs = [...filteredLogs].sort((a, b) => {
        try {
          // Parse timestamps like "Mar 24, 2025 - 6:58 PM"
          const dateA = parse(a.timestamp, "MMM d, yyyy - h:mm a", new Date());
          const dateB = parse(b.timestamp, "MMM d, yyyy - h:mm a", new Date());
          return compareDesc(dateA, dateB); // compareDesc for newest first
        } catch (error) {
          console.error("Error parsing dates:", error);
          return 0; // Keep original order if parsing fails
        }
      });

      setLogs(sortedLogs);
      setError(null);
    } catch (err) {
      console.error("Error fetching logs:", err);
      setError("Failed to fetch logs. Please try again later.");
    } finally {
      setLoading(false);
    }
  }, [entityKey, entityType]);

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  if (loading) {
    return <div className="text-center py-8">Loading logs...</div>;
  }

  if (error) {
    return (
      <div className="border border-red-200 bg-red-50 p-4 rounded-md">
        <h2 className="font-semibold text-red-800">Error</h2>
        <p className="mt-2 text-red-700">
          {error || "Something went wrong. Please try again later."}
        </p>
        <Button
          variant="outline"
          size="sm"
          className="mt-4"
          onClick={fetchLogs}
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <Card className="shadow-md">
      <CardHeader className="bg-muted/50">
        <div className="flex justify-between items-center">
          <CardTitle className="text-xl flex items-center">
            <span>Application Logs</span>
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchLogs}
            disabled={loading}
            className="flex items-center gap-1"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
            <span>Refresh</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {logs.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8">
            <p className="text-muted-foreground text-center mb-4">
              No logs found for this entity
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto w-full">
            <Table className="w-full table-fixed">
              <TableHeader>
                <TableRow className="bg-gray-100">
                  <TableHead className="w-[14%] font-bold text-black">
                    Timestamp (UTC)
                  </TableHead>
                  <TableHead className="w-[10%] font-bold text-black">
                    Type
                  </TableHead>
                  <TableHead className="w-[12%] font-bold text-black">
                    Status
                  </TableHead>
                  <TableHead className="w-[12%] font-bold text-black">
                    Source
                  </TableHead>
                  <TableHead className="w-[14%] font-bold text-black">
                    Destination
                  </TableHead>
                  <TableHead className="w-[18%] font-bold text-black">
                    Usecase
                  </TableHead>
                  <TableHead className="w-[20%] font-bold text-black">
                    Message
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {logs.map((log: LogEntry, index: number) => (
                  <TableRow
                    key={log.request_id || index}
                    className={index % 2 === 0 ? "bg-muted/20" : ""}
                  >
                    <TableCell
                      className="text-xs whitespace-nowrap w-[14%]"
                      title={log.timestamp}
                    >
                      {log.timestamp}
                    </TableCell>
                    <TableCell className="w-[10%] ">
                      <Badge
                        variant={getStatusBadgeVariant(log.type)}
                        className="whitespace-nowrap text-xs"
                        title={log.type}
                      >
                        {log.type}
                      </Badge>
                    </TableCell>
                    <TableCell className="w-[12%]">
                      <Badge
                        variant={getStatusText(log.status)}
                        className="whitespace-nowrap text-xs"
                        title={log.status}
                      >
                        {log.status.includes("Completed Successfully")
                          ? "Success"
                          : log.status}
                      </Badge>
                    </TableCell>
                    <TableCell
                      className="text-sm truncate w-[12%]"
                      title={log.source || "-"}
                    >
                      {log.source || "-"}
                    </TableCell>
                    <TableCell
                      className="text-sm truncate w-[14%]"
                      title={log.destination || "-"}
                    >
                      {log.destination || "-"}
                    </TableCell>
                    <TableCell
                      className="text-sm truncate w-[18%]"
                      title={log.usecase || "-"}
                    >
                      {log.usecase || "-"}
                    </TableCell>
                    <TableCell
                      className="text-sm truncate w-[20%]"
                      title={log.message}
                    >
                      {log.message}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
