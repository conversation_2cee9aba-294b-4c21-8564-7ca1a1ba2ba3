"use client";

import { useSearchParams } from "next/navigation";
import LogsTable from "./LogsTable";

export default function SearchParamsLoader() {
  // This will trigger suspense which is handled by the parent
  const searchParams = useSearchParams();
  const entityKey = searchParams.get("entityKey") || "";
  const entityType = searchParams.get("entityType") || "";
  const auth_token = searchParams.get("auth_token") || "";

  if (!entityKey || !entityType || !auth_token) {
    return (
      <div className="border border-yellow-200 bg-yellow-50 p-4 rounded-md">
        <h2 className="font-semibold text-yellow-800">Missing Parameters</h2>
        <p className="mt-2 text-yellow-700">
          Please provide both entityKey, entityType and auth_token as URL
          parameters.
        </p>
        <p className="mt-2 text-sm text-yellow-600">
          Example:
          /?entityKey=abc123&entityType=Application_Form_Id__c&auth_token=xyz456
        </p>
      </div>
    );
  }

  return (
    <LogsTable
      entityKey={entityKey}
      entityType={entityType}
      authToken={auth_token}
    />
  );
}
